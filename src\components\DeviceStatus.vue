<template>
  <div class="device-status">
    <div class="section-title">设备状态</div>
    
    <div class="status-list">
      <div 
        v-for="device in deviceList" 
        :key="device.name"
        class="status-item"
      >
        <div class="device-name">{{ device.name }}</div>
        <div class="device-counts">
          <span class="status-count online">{{ device.online }}</span>
          <span class="separator">/</span>
          <span class="status-count offline">{{ device.offline }}</span>
          <span class="separator">/</span>
          <span class="status-count error">{{ device.error }}</span>
        </div>
        <div class="status-labels">
          <span class="status-label online">在线</span>
          <span class="status-label offline">离线</span>
          <span class="status-label error">异常</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface DeviceStatus {
  name: string
  online: number
  offline: number
  error: number
}

interface Props {
  buildingId: number
}

const props = defineProps<Props>()

// 根据楼栋ID生成设备状态数据
const generateDeviceStatus = (buildingId: number): DeviceStatus[] => {
  // 基础设备数量，根据楼栋ID有所变化
  const baseMultiplier = 0.8 + (buildingId - 1) * 0.05
  
  return [
    {
      name: '闸坝',
      online: Math.floor(3 * baseMultiplier) || 1,
      offline: Math.floor(Math.random() * 2),
      error: Math.floor(Math.random() * 2)
    },
    {
      name: '摄像头',
      online: Math.floor(123 * baseMultiplier),
      offline: Math.floor(4 * baseMultiplier),
      error: Math.floor(5 * baseMultiplier)
    },
    {
      name: '门禁',
      online: Math.floor(8 * baseMultiplier),
      offline: Math.floor(Math.random() * 3),
      error: Math.floor(Math.random() * 2)
    },
    {
      name: '水泵',
      online: Math.floor(24 * baseMultiplier),
      offline: Math.floor(2 * baseMultiplier),
      error: Math.floor(Math.random() * 3)
    },
    {
      name: '水质探头',
      online: Math.floor(36 * baseMultiplier),
      offline: Math.floor(3 * baseMultiplier),
      error: Math.floor(Math.random() * 4)
    },
    {
      name: '风机',
      online: Math.floor(18 * baseMultiplier),
      offline: Math.floor(Math.random() * 3),
      error: Math.floor(Math.random() * 2)
    },
    {
      name: '臭氧',
      online: Math.floor(12 * baseMultiplier),
      offline: Math.floor(Math.random() * 2),
      error: Math.floor(Math.random() * 2)
    },
    {
      name: '消毒灯',
      online: Math.floor(48 * baseMultiplier),
      offline: Math.floor(4 * baseMultiplier),
      error: Math.floor(Math.random() * 3)
    }
  ]
}

const deviceList = ref<DeviceStatus[]>([])

// 监听楼栋变化
watch(() => props.buildingId, (newBuildingId) => {
  deviceList.value = generateDeviceStatus(newBuildingId)
}, { immediate: true })
</script>

<style scoped lang="scss">
.device-status {
  height: 100%;
}

.section-title {
  text-align: center;
  margin-bottom: 15px;
  font-size: 16px;
  color: #00d4ff;
  font-weight: bold;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid #333;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #00d4ff;
    background: rgba(0, 212, 255, 0.1);
  }
  
  .device-name {
    font-size: 14px;
    color: #fff;
    font-weight: bold;
    margin-bottom: 8px;
    text-align: center;
  }
  
  .device-counts {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-bottom: 6px;
    
    .status-count {
      font-size: 16px;
      font-weight: bold;
      min-width: 20px;
      text-align: center;
      
      &.online {
        color: #52c41a;
      }
      
      &.offline {
        color: #faad14;
      }
      
      &.error {
        color: #ff4d4f;
      }
    }
    
    .separator {
      color: #666;
      font-size: 14px;
    }
  }
  
  .status-labels {
    display: flex;
    justify-content: space-around;
    
    .status-label {
      font-size: 10px;
      
      &.online {
        color: #52c41a;
      }
      
      &.offline {
        color: #faad14;
      }
      
      &.error {
        color: #ff4d4f;
      }
    }
  }
}
</style>
