<template>
  <div class="homepage-container">
    <!-- 标题 -->
    <div class="header">
      <h1 class="title">广西银海集团智能化养殖平台</h1>
      <div class="current-time">{{ currentTime }}</div>
    </div>

    <!-- 主体内容 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-panel">
        <!-- 天气情况 -->
        <div class="panel-section">
          <WeatherWidget />
        </div>

        <!-- 生产情况 -->
        <div class="panel-section">
          <ProductionTable :building-id="currentBuildingId" />
        </div>

        <!-- 水质监测 -->
        <div class="panel-section">
          <WaterQualityMonitor :building-id="currentBuildingId" />
        </div>
      </div>

      <!-- 中间区域 -->
      <div class="center-panel">
        <!-- 统计信息 -->
        <div class="stats-section">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ yearlyStats.totalPower }}</div>
              <div class="stat-label">年度累计总耗电量(kWh)</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ yearlyStats.totalFeed }}</div>
              <div class="stat-label">年度累计投喂量(kg)</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ yearlyStats.totalOutput }}</div>
              <div class="stat-label">年度累计总产量(kg)</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ yearlyStats.totalPumpIn }}</div>
              <div class="stat-label">年度累计抽水量(m³)</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ yearlyStats.totalPumpOut }}</div>
              <div class="stat-label">年度累计排水量(m³)</div>
            </div>
          </div>
        </div>

        <!-- 9栋楼 -->
        <div class="buildings-section">
          <div class="buildings-grid">
            <div
              v-for="building in buildings"
              :key="building.id"
              :class="[
                'building-item',
                { active: building.id === currentBuildingId },
              ]"
              @click="selectBuilding(building.id)"
            >
              <div class="building-number">{{ building.name }}</div>

              <!-- 选中楼栋的弹窗 -->
              <div
                v-if="building.id === currentBuildingId"
                class="building-popup"
              >
                <div class="popup-content">
                  <h4>{{ building.name }}</h4>
                  <div class="popup-stats">
                    <div>当前耗电量: {{ building.currentPower }}kW</div>
                    <div>养殖池数: {{ building.poolCount }}个</div>
                    <div>预计产量: {{ building.expectedOutput }}kg</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 监控画面 -->
        <div class="monitor-section">
          <div class="monitor-grid">
            <div class="monitor-item">
              <div class="monitor-title">生产楼宇监控</div>
              <div class="monitor-screen">
                <img
                  src="https://picsum.photos/300/200?random=1"
                  alt="生产楼宇监控"
                />
              </div>
            </div>
            <div class="monitor-item">
              <div class="monitor-title">园区监控</div>
              <div class="monitor-screen">
                <img
                  src="https://picsum.photos/300/200?random=2"
                  alt="园区监控"
                />
              </div>
            </div>
            <div class="monitor-item">
              <div class="monitor-title">闸坝监控</div>
              <div class="monitor-screen">
                <img
                  src="https://picsum.photos/300/200?random=3"
                  alt="闸坝监控"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-panel">
        <!-- 设备状态 -->
        <div class="panel-section">
          <DeviceStatus :building-id="currentBuildingId" />
        </div>

        <!-- 虾苗生长趋势 -->
        <div class="panel-section">
          <ShrimpGrowthTrend :building-id="currentBuildingId" />
        </div>

        <!-- 水下高清摄像头 -->
        <div class="panel-section">
          <div class="section-title">水下高清摄像头</div>
          <div class="underwater-camera">
            <img
              src="https://picsum.photos/400/300?random=4"
              alt="水下高清摄像头"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import dayjs from "dayjs";
import WeatherWidget from "../components/WeatherWidget.vue";
import ProductionTable from "../components/ProductionTable.vue";
import WaterQualityMonitor from "../components/WaterQualityMonitor.vue";
import DeviceStatus from "../components/DeviceStatus.vue";
import ShrimpGrowthTrend from "../components/ShrimpGrowthTrend.vue";

// 当前时间
const currentTime = ref("");
const updateTime = () => {
  currentTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss");
};

// 当前选中的楼栋
const currentBuildingId = ref(1);

// 年度统计数据
const yearlyStats = ref({
  totalPower: "1,234,567",
  totalFeed: "456,789",
  totalOutput: "789,123",
  totalPumpIn: "2,345,678",
  totalPumpOut: "1,876,543",
});

// 9栋楼数据
const buildings = ref([
  {
    id: 1,
    name: "1#楼",
    currentPower: 125.6,
    poolCount: 24,
    expectedOutput: 15600,
  },
  {
    id: 2,
    name: "2#楼",
    currentPower: 132.4,
    poolCount: 24,
    expectedOutput: 16200,
  },
  {
    id: 3,
    name: "3#楼",
    currentPower: 118.9,
    poolCount: 20,
    expectedOutput: 14800,
  },
  {
    id: 4,
    name: "4#楼",
    currentPower: 145.2,
    poolCount: 28,
    expectedOutput: 17400,
  },
  {
    id: 5,
    name: "5#楼",
    currentPower: 128.7,
    poolCount: 24,
    expectedOutput: 15900,
  },
  {
    id: 6,
    name: "6#楼",
    currentPower: 135.1,
    poolCount: 26,
    expectedOutput: 16800,
  },
  {
    id: 7,
    name: "7#楼",
    currentPower: 122.3,
    poolCount: 22,
    expectedOutput: 15200,
  },
  {
    id: 8,
    name: "8#楼",
    currentPower: 140.8,
    poolCount: 26,
    expectedOutput: 17100,
  },
  {
    id: 9,
    name: "9#楼",
    currentPower: 129.5,
    poolCount: 24,
    expectedOutput: 16000,
  },
]);

// 选择楼栋
const selectBuilding = (buildingId: number) => {
  currentBuildingId.value = buildingId;
};

// 定时器
let timeTimer: number;
let buildingTimer: number;

onMounted(() => {
  // 更新时间
  updateTime();
  timeTimer = setInterval(updateTime, 1000);

  // 定时切换楼栋
  buildingTimer = setInterval(() => {
    const nextId =
      currentBuildingId.value >= 9 ? 1 : currentBuildingId.value + 1;
    currentBuildingId.value = nextId;
  }, 10000); // 每10秒切换一次
});

onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer);
  if (buildingTimer) clearInterval(buildingTimer);
});
</script>

<style scoped lang="scss">
.homepage-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1445 0%, #1a2980 100%);
  color: #fff;
  overflow: hidden;
  font-family: "Microsoft YaHei", sans-serif;
}

.header {
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 2px solid #00d4ff;

  .title {
    font-size: 36px;
    font-weight: bold;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
  }

  .current-time {
    font-size: 18px;
    color: #00d4ff;
  }
}

.main-content {
  height: calc(100vh - 80px);
  display: flex;
  gap: 20px;
  padding: 20px;
}

.left-panel,
.right-panel {
  width: 25%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.center-panel {
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.panel-section {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.stats-section {
  height: 120px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  height: 100%;
}

.stat-item {
  text-align: center;

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #00d4ff;
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 12px;
    color: #ccc;
  }
}

.buildings-section {
  flex: 1;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  padding: 20px;
  backdrop-filter: blur(10px);
  position: relative;
}

.buildings-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  height: 100%;
}

.building-item {
  background: rgba(0, 100, 200, 0.3);
  border: 2px solid #0066cc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &.active {
    background: rgba(0, 212, 255, 0.3);
    border-color: #00d4ff;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
  }

  .building-number {
    font-size: 18px;
    font-weight: bold;
  }
}

.building-popup {
  position: absolute;
  top: -120px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  padding: 15px;
  min-width: 200px;
  z-index: 10;

  .popup-content h4 {
    margin: 0 0 10px 0;
    color: #00d4ff;
    text-align: center;
  }

  .popup-stats div {
    margin: 5px 0;
    font-size: 14px;
  }
}

.monitor-section {
  height: 200px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  height: 100%;
}

.monitor-item {
  .monitor-title {
    text-align: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: #00d4ff;
  }

  .monitor-screen {
    height: calc(100% - 30px);
    border: 1px solid #666;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.section-title {
  text-align: center;
  margin-bottom: 15px;
  font-size: 16px;
  color: #00d4ff;
  font-weight: bold;
}

.underwater-camera {
  height: 200px;
  border: 1px solid #666;
  border-radius: 4px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
